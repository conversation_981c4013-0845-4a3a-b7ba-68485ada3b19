# Master Workflow Log Management System

A comprehensive log management solution that addresses ownership issues, implements intelligent rotation, provides size monitoring, and ensures efficient cleanup across the entire workflow system.

## Overview

The Master Workflow Log Management System provides:

- **Ownership Resolution**: Fixes UID 1000 vs UID 1001 ownership conflicts
- **Intelligent Rotation**: Multi-tier rotation based on file types and usage patterns  
- **Size Monitoring**: Real-time monitoring with automated alerts and responses
- **Smart Cleanup**: Retention policies based on log categories and compliance requirements
- **Health Monitoring**: Comprehensive reporting and status tracking

## Quick Start

### 1. Fix Ownership Issues
```bash
# Check what needs fixing
./scripts/fix-log-ownership.sh --dry-run

# Fix ownership issues
./scripts/fix-log-ownership.sh
```

### 2. Setup Log Rotation
```bash
# Setup comprehensive log rotation
./scripts/setup-log-rotation.sh

# Test configuration
./scripts/setup-log-rotation.sh --test
```

### 3. Monitor Log Sizes
```bash
# Check current log sizes
./scripts/log-size-monitor.sh check

# Generate monitoring report
./scripts/log-size-monitor.sh report
```

### 4. Cleanup Old Logs
```bash
# Automatic cleanup
./scripts/cleanup-logs.sh auto

# Emergency cleanup if needed
./scripts/cleanup-logs.sh emergency
```

## System Components

### 1. Ownership Management (`fix-log-ownership.sh`)

**Purpose**: Resolves the UID 1000 vs UID 1001 ownership issue affecting log files.

**Key Features**:
- Multi-strategy ownership fixing (direct chown, copy-replace, recursive copy)
- Comprehensive directory scanning and processing
- Dry-run mode for safe testing
- Detailed logging of all operations
- Creates standard log directory structure

**Usage Examples**:
```bash
# Fix ownership issues
./scripts/fix-log-ownership.sh

# Test what would be changed
./scripts/fix-log-ownership.sh --dry-run

# Get help
./scripts/fix-log-ownership.sh --help
```

### 2. Log Rotation System

**Configuration File**: `/workspaces/MASTER-WORKFLOW/configs/logrotate.conf`

**Rotation Policies**:
- **Workflow System Logs**: Daily rotation, 30-day retention, 50MB max size
- **Session Logs**: Daily rotation, 14-day retention, 10MB max size  
- **Agent Logs**: Daily rotation, 14-day retention, 25MB max size
- **JSONL Logs**: Daily rotation, 60-day retention, 100MB max size
- **Error Logs**: Daily rotation, 90-day retention, 100MB max size
- **Performance Logs**: Daily rotation, 14-day retention, 50MB max size
- **Debug Logs**: Daily rotation, 7-day retention, 25MB max size

**Setup Commands**:
```bash
# Setup comprehensive log rotation
./scripts/setup-log-rotation.sh

# Test configuration
./scripts/setup-log-rotation.sh --test

# Check status
./scripts/setup-log-rotation.sh --status
```

### 3. Size Monitoring (`log-size-monitor.sh`)

**Monitoring Thresholds**:
- **Warning**: 50MB (configurable by log type)
- **Critical**: 100MB (triggers immediate attention)
- **Emergency**: 200MB (triggers automatic rotation)
- **Total Limit**: 1GB across all logs

**Size Limits by Category**:
- Application: 100MB
- System: 50MB  
- Workflow: 200MB
- Performance: 75MB
- Security: 25MB
- Monitoring: 50MB

**Usage Commands**:
```bash
# Check current log sizes
./scripts/log-size-monitor.sh check

# Generate detailed report
./scripts/log-size-monitor.sh report

# Start continuous monitoring
./scripts/log-size-monitor.sh monitor

# Emergency cleanup
./scripts/log-size-monitor.sh emergency
```

### 4. Cleanup Management (`cleanup-logs.sh`)

**Retention Policies**:
- **Application Logs**: 7 days
- **System Logs**: 30 days
- **Workflow Logs**: 14 days
- **Performance Logs**: 7 days  
- **Security Logs**: 90 days (compliance requirement)
- **Monitoring Logs**: 30 days
- **Debug Logs**: 3 days
- **Error Logs**: 60 days
- **Audit Logs**: 365 days (compliance requirement)

**Cleanup Commands**:
```bash
# Automatic cleanup based on policies
./scripts/cleanup-logs.sh auto

# Quick cleanup (compressed only)
./scripts/cleanup-logs.sh quick

# Full cleanup (all categories)
./scripts/cleanup-logs.sh full

# Dry run simulation
./scripts/cleanup-logs.sh dry-run

# Emergency cleanup
./scripts/cleanup-logs.sh emergency
```

## Directory Structure

```
/workspaces/MASTER-WORKFLOW/
├── .ai-workflow/logs/           # AI Workflow system logs
│   ├── installation.log
│   ├── manifest-writer.log
│   ├── supervisor.log
│   ├── workflow.log
│   ├── agent-bus.jsonl
│   ├── log-management.log       # Central management log
│   ├── agents/                  # Agent-specific logs
│   └── sessions/                # Session logs
├── .claude-flow/memory/logs/    # Claude Flow memory logs
├── engine/.ai-workflow/logs/    # Engine workflow logs
├── logs/                        # Main log directory
│   ├── application/             # Application logs
│   ├── system/                  # System logs
│   ├── workflow/                # Workflow execution logs
│   ├── performance/             # Performance monitoring
│   ├── security/                # Security and audit logs
│   ├── monitoring/              # Log management monitoring
│   ├── archive/                 # Archived logs
│   └── state/                   # Logrotate state files
├── configs/
│   └── logrotate.conf           # Main rotation configuration
└── scripts/
    ├── fix-log-ownership.sh     # Ownership correction
    ├── setup-log-rotation.sh    # Rotation setup
    ├── log-size-monitor.sh      # Size monitoring
    ├── cleanup-logs.sh          # Cleanup management
    ├── rotate-logs-manual.sh    # Manual rotation
    └── log-rotation-status.sh   # Status checking
```

## Automation Setup

### Cron Jobs

The system automatically sets up these scheduled tasks:

```bash
# Log rotation - daily at 2:00 AM
0 2 * * * /usr/sbin/logrotate -s /workspaces/MASTER-WORKFLOW/logs/logrotate.state /workspaces/MASTER-WORKFLOW/configs/logrotate.conf

# Size monitoring - every 30 minutes
*/30 * * * * /workspaces/MASTER-WORKFLOW/scripts/log-size-monitor.sh check

# Cleanup - daily at 3:00 AM
0 3 * * * /workspaces/MASTER-WORKFLOW/scripts/cleanup-logs.sh auto
```

### Manual Execution

For immediate action or testing:

```bash
# Manual rotation
./scripts/rotate-logs-manual.sh

# Check rotation status
./scripts/log-rotation-status.sh

# Monitor service
./scripts/log-monitor-service.sh
```

## Monitoring and Alerts

### Central Management Log

All operations are logged to: `/workspaces/MASTER-WORKFLOW/.ai-workflow/logs/log-management.log`

### Alert Levels

1. **INFO**: Normal operations and status updates
2. **WARNING**: Size thresholds exceeded, potential issues
3. **ERROR**: Failed operations, configuration issues
4. **CRITICAL**: Emergency conditions requiring immediate attention
5. **ALERT**: System-level issues, policy violations

### Reporting

Generated reports include:

- **Size Monitoring Report**: Current sizes, trends, projections
- **Cleanup Report**: Retention compliance, cleanup statistics
- **Rotation Status**: Last rotation times, configuration status
- **Health Report**: Overall system health and recommendations

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Ownership Problems

**Issue**: Log files owned by UID 1000 instead of UID 1001 (node user)

**Solution**:
```bash
# Check current ownership issues
./scripts/fix-log-ownership.sh --dry-run

# Fix ownership
./scripts/fix-log-ownership.sh
```

#### 2. Rotation Not Working

**Issue**: Log files are growing but not being rotated

**Solution**:
```bash
# Test rotation configuration
./scripts/setup-log-rotation.sh --test

# Check rotation status
./scripts/log-rotation-status.sh

# Manual rotation
./scripts/rotate-logs-manual.sh
```

#### 3. Large Log Files

**Issue**: Individual log files are too large

**Solution**:
```bash
# Check sizes
./scripts/log-size-monitor.sh check

# Emergency cleanup
./scripts/log-size-monitor.sh emergency

# Force rotation
./scripts/rotate-logs-manual.sh
```

#### 4. Disk Space Issues

**Issue**: Running out of disk space due to logs

**Solution**:
```bash
# Check current usage
du -sh /workspaces/MASTER-WORKFLOW/.ai-workflow/logs/
du -sh /workspaces/MASTER-WORKFLOW/logs/

# Full cleanup
./scripts/cleanup-logs.sh full

# Emergency mode
./scripts/cleanup-logs.sh emergency
```

#### 5. Permission Denied Errors

**Issue**: Scripts can't access or modify log files

**Solution**:
```bash
# Fix ownership
./scripts/fix-log-ownership.sh

# Check permissions
ls -la /workspaces/MASTER-WORKFLOW/.ai-workflow/logs/

# Fix permissions manually if needed
chmod 755 /workspaces/MASTER-WORKFLOW/.ai-workflow/logs/
chmod 644 /workspaces/MASTER-WORKFLOW/.ai-workflow/logs/*.log
```

### Log Locations for Debugging

- **Management Log**: `.ai-workflow/logs/log-management.log`
- **Size Monitor Log**: `logs/monitoring/size-monitor.log`
- **Cleanup Log**: `logs/monitoring/cleanup.log`
- **Rotation Status**: Output of `log-rotation-status.sh`

## Security and Compliance

### File Permissions

- **Log directories**: 755 (rwxr-xr-x)
- **Log files**: 644 (rw-r--r--)
- **Scripts**: 755 (rwxr-xr-x)
- **Configuration files**: 644 (rw-r--r--)

### Compliance Features

- **Audit Log Retention**: 365 days minimum
- **Security Log Retention**: 90 days minimum
- **Tamper Protection**: Ownership and permission enforcement
- **Access Logging**: All management operations logged

### User Access Control

- All operations run as `node` user (UID 1001)
- No elevated privileges required for normal operation
- Automatic ownership correction maintains proper access

## Performance Optimization

### Resource Usage

- **CPU**: Minimal impact during normal operation
- **Memory**: Low memory footprint for monitoring
- **Disk I/O**: Controlled through intelligent scheduling
- **Network**: No network dependencies

### Optimization Features

- **Compression**: Automatic compression of rotated logs
- **Intelligent Cleanup**: Category-based retention policies
- **Size Monitoring**: Proactive size management
- **Efficient Rotation**: Copy-truncate to avoid service interruption

## Best Practices

### Daily Operations

1. **Monitor**: Check daily size reports
2. **Review**: Examine management log for issues
3. **Validate**: Ensure rotation and cleanup are functioning
4. **Optimize**: Adjust retention policies as needed

### Maintenance Schedule

1. **Weekly**: Review cleanup reports and adjust policies
2. **Monthly**: Validate disk usage trends and capacity planning
3. **Quarterly**: Review and update retention policies
4. **Annually**: Audit compliance with retention requirements

### Configuration Tuning

1. **Size Limits**: Adjust based on actual usage patterns
2. **Retention**: Balance storage costs vs. compliance needs
3. **Rotation Frequency**: Optimize based on log growth rates
4. **Monitoring Intervals**: Balance responsiveness vs. resource usage

## Emergency Procedures

### Disk Space Crisis

1. **Immediate Response**:
   ```bash
   ./scripts/cleanup-logs.sh emergency
   ```

2. **Short-term Fix**:
   ```bash
   ./scripts/log-size-monitor.sh emergency
   ```

3. **Long-term Solution**: Review and adjust retention policies

### System Recovery

1. **Ownership Reset**:
   ```bash
   ./scripts/fix-log-ownership.sh
   ```

2. **Configuration Rebuild**:
   ```bash
   ./scripts/setup-log-rotation.sh
   ```

3. **Monitoring Restart**:
   ```bash
   ./scripts/log-size-monitor.sh setup
   ```

4. **Validation**:
   ```bash
   ./scripts/setup-log-rotation.sh --test
   ./scripts/log-size-monitor.sh check
   ./scripts/cleanup-logs.sh status
   ```

## System Integration

### With AI Workflow System

- Monitors all AI workflow component logs
- Integrates with agent logging
- Supports session-based log organization
- Handles JSONL structured logs

### With External Systems

- Logrotate integration for system-level rotation
- Cron/systemd integration for scheduling
- File system monitoring integration
- Alert system integration capabilities

## Configuration Reference

### Logrotate Configuration Options

```bash
# Global settings
compress                 # Compress rotated logs
compresscmd /bin/gzip   # Compression command
delaycompress           # Delay compression until next rotation
missingok               # Don't error if log file missing
notifempty              # Don't rotate empty files
sharedscripts           # Run scripts once for all files
copytruncate            # Copy and truncate instead of moving
```

### Size Monitoring Limits

```bash
# Configurable in log-size-monitor.sh
declare -A SIZE_LIMITS=(
    ["application"]=100
    ["system"]=50
    ["workflow"]=200
    ["performance"]=75
    ["security"]=25
    ["default"]=50
)
```

### Cleanup Retention Policies

```bash
# Configurable in cleanup-logs.sh
declare -A RETENTION_POLICIES=(
    ["application"]=7      # 1 week
    ["system"]=30          # 1 month
    ["workflow"]=14        # 2 weeks
    ["performance"]=7      # 1 week
    ["security"]=90        # 3 months (compliance)
    ["monitoring"]=30      # 1 month
    ["debug"]=3            # 3 days
    ["error"]=60           # 2 months
    ["audit"]=365          # 1 year (compliance)
    ["default"]=14         # 2 weeks
)
```

## Support and Maintenance

For issues or questions:

1. Check the troubleshooting guide above
2. Review log files in `.ai-workflow/logs/log-management.log`
3. Run diagnostic commands:
   ```bash
   ./scripts/fix-log-ownership.sh --dry-run
   ./scripts/setup-log-rotation.sh --test
   ./scripts/log-size-monitor.sh check
   ./scripts/cleanup-logs.sh status
   ```

This log management system ensures reliable, efficient, and compliant log handling for the Master Workflow system while addressing the specific UID ownership challenges in the container environment.