{"timestamp": "2025-08-18T17:22:59.963Z", "duration": {"total": 44588.030171, "formatted": "44.59 seconds"}, "system": {"platform": "linux", "arch": "x64", "cpus": 12, "totalMemory": 29.22, "freeMemory": 15.87, "nodeVersion": "v20.19.4", "v8Version": "11.3.244.8-node.29"}, "performance": {"agentSpawn": {"average": 679.6913286875001, "min": 264.1667889999999, "max": 996.3558330000001, "p95": 996.3558330000001, "p99": 996.3558330000001, "target": 1000, "withinTarget": 1}, "taskDistribution": {"averageRate": 455589.167908774, "maxRate": 455589.167908774, "totalTasksDistributed": 50}, "memory": {"baseline": {"rss": 45088768, "heapTotal": 4837376, "heapUsed": 4191816, "external": 1547855, "arrayBuffers": 10531}, "final": {"rss": 46456832, "heapTotal": 12701696, "heapUsed": 3941288, "external": 1660853, "arrayBuffers": 10491}, "increase": -250528, "peakUsage": null, "efficiency": "106.4"}, "network": [{"operation": "Agent Communication", "expectedLatency": 10, "actualLatency": 10.563285999996879, "overhead": 0.5632859999968787, "efficiency": 94.66751160579156}, {"operation": "Task Distribution", "expectedLatency": 25, "actualLatency": 25.381960999999137, "overhead": 0.38196099999913713, "efficiency": 98.4951477941395}, {"operation": "Result Aggregation", "expectedLatency": 15, "actualLatency": 14.795577999997477, "overhead": -0.20442200000252342, "efficiency": 101.3816425421336}, {"operation": "Status Updates", "expectedLatency": 5, "actualLatency": 5.392705000005662, "overhead": 0.39270500000566244, "efficiency": 92.71784753652851}], "resourceUtilization": [{"agents": 1, "memoryUsage": {"rss": 45219840, "heapTotal": 4837376, "heapUsed": 4227992, "external": 1547855, "arrayBuffers": 10531}, "memoryIncrease": 36176, "memoryPerAgent": 36176, "avgSpawnTime": 604.7271460000001, "maxSpawnTime": 604.7271460000001, "scaleTime": 604.893478, "timestamp": 1755537740607}, {"agents": 5, "memoryUsage": {"rss": 45219840, "heapTotal": 4837376, "heapUsed": 4271568, "external": 1547855, "arrayBuffers": 10531}, "memoryIncrease": 79752, "memoryPerAgent": 15950.4, "avgSpawnTime": 705.7653054, "maxSpawnTime": 996.3558330000001, "scaleTime": 1261.665359, "timestamp": 1755537741563}, {"agents": 10, "memoryUsage": {"rss": 45219840, "heapTotal": 4837376, "heapUsed": 4333840, "external": 1547855, "arrayBuffers": 10531}, "memoryIncrease": 142024, "memoryPerAgent": 14202.4, "avgSpawnTime": 674.1507586000001, "maxSpawnTime": 978.1448790000004, "scaleTime": 1605.8320789999998, "timestamp": 1755537745172}]}, "bottlenecks": {"identified": {"performance": [], "memory": [], "resource": [], "io": []}, "count": 0, "critical": 0, "warnings": 0}, "optimizations": {"verified": {"caching": {"implemented": false, "description": "No explicit caching mechanisms found", "impact": "High", "recommendation": "Implement caching for frequently accessed data and computation results"}, "lazyLoading": {"implemented": false, "description": "Components loaded eagerly at startup", "impact": "Medium", "recommendation": "Implement lazy loading for optional modules and agents"}, "asyncOperations": {"implemented": true, "description": "Async operations efficient (50ms parallel vs 504ms sequential)", "impact": "High", "recommendation": "Continue using Promise.all for concurrent operations"}, "resourcePooling": {"implemented": false, "description": "No resource pooling patterns found", "impact": "Medium", "recommendation": "Implement resource pooling for agents and connections"}}, "recommendations": [{"type": "missing_optimization", "category": "caching", "impact": "High", "recommendation": "Implement caching for frequently accessed data and computation results"}, {"type": "missing_optimization", "category": "lazyLoading", "impact": "Medium", "recommendation": "Implement lazy loading for optional modules and agents"}, {"type": "missing_optimization", "category": "resourcePooling", "impact": "Medium", "recommendation": "Implement resource pooling for agents and connections"}]}, "testResults": {"total": 101, "byLevel": {"test": 16, "info": 43, "performance": 24, "success": 14, "warning": 3, "optimization": 1}, "errors": []}, "targets": {"agentSpawnTime": {"target": 1000, "achieved": true}, "memoryPerAgent": {"target": 61440, "achieved": true}, "contextWindow": {"target": 200000, "achieved": true}}}