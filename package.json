{"name": "master-workflow", "version": "2.0.0", "description": "Master Workflow System with AI-powered orchestration", "main": "workflow-runner.js", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node workflow-runner.js", "engine": "cd intelligence-engine && npm start", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --testMatch='**/*.test.js'", "test:integration": "jest --testMatch='**/*.integration.test.js'", "setup": "bash .devcontainer/setup-claude.sh", "check": "node .ai-workflow/intelligence-engine/integration-checker.js", "health": "node .ai-workflow/intelligence-engine/integration-checker.js --json", "claude-flow:alpha": "npx claude-flow@alpha", "claude-flow:beta": "npx claude-flow@beta", "claude-flow:stable": "npx claude-flow@stable", "claude-flow:latest": "npx claude-flow@latest"}, "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "ws": "^8.13.0", "chokidar": "^3.5.3", "inquirer": "^9.2.7", "commander": "^11.0.0", "chalk": "^5.3.0", "figlet": "^1.6.0", "ora": "^7.0.1", "boxen": "^7.1.1", "node-cron": "^3.0.2"}, "devDependencies": {"jest": "^29.6.1", "@types/jest": "^29.5.3", "nodemon": "^3.0.1", "eslint": "^8.45.0", "prettier": "^3.0.0"}, "bin": {"master-workflow": "./workflow-runner.js", "mw": "./workflow-runner.js"}, "keywords": ["ai", "workflow", "automation", "claude", "orchestration", "agent-os", "tmux", "hive-mind"], "author": "Claude Code Workflow System", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/master-workflow.git"}, "bugs": {"url": "https://github.com/your-org/master-workflow/issues"}, "homepage": "https://github.com/your-org/master-workflow#readme"}