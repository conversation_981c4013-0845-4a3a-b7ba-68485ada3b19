MIT License

Copyright (c) 2025 Intelligent Workflow Decision System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Additional Attribution

This project incorporates and builds upon several open-source technologies:

### Claude Code Integration
- Claude Code CLI by Anthropic
- Used under their respective licenses
- Enhanced with custom workflow orchestration

### Multi-Agent Frameworks
- Claude Flow 2.0 integration
- Agent-OS specification support
- Custom coordination mechanisms

### Development Tools
- Node.js ecosystem components
- Various npm packages (see package.json for complete list)
- Cross-platform compatibility libraries

### Documentation and Templates
- GitHub templates based on community best practices
- Changelog format from Keep a Changelog
- Semantic versioning specification compliance

All third-party components maintain their original licenses and attribution.
This project's custom code and integration work is provided under the MIT License above.