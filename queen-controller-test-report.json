{"timestamp": "2025-08-18T16:47:17.908Z", "duration": {"total": 3124.099294, "formatted": "3.12 seconds"}, "memory": {"baseline": {"rss": 44957696, "heapTotal": 4694016, "heapUsed": 4257464, "external": 1562356, "arrayBuffers": 10515}, "final": {"rss": 52658176, "heapTotal": 9150464, "heapUsed": 6058208, "external": 1753053, "arrayBuffers": 201212}, "increase": {"heap": 1800744, "rss": 7700480, "external": 190697}}, "queenController": {"finalStatus": {"active": 29, "activeAgents": 29, "queued": 6, "pending": 2, "completed": 1, "maxConcurrent": 10, "metrics": {"agentsSpawned": 31, "tasksDistributed": 30, "tasksCompleted": 1, "averageCompletionTime": 765, "contextUsage": {}, "errors": [{"agentId": "test-runner-1755535635809-48shfub5h", "type": "test-runner", "error": "Simulated agent failure for testing", "timestamp": 1755535637885}]}, "agents": [{"id": "code-analyzer-1755535637111-<PERSON><PERSON><PERSON><PERSON>", "type": "code-analyzer", "status": "completed", "tokenUsage": 57543, "runtime": 765}, {"id": "test-runner-1755535635809-48shfub5h", "type": "test-runner", "status": "error", "tokenUsage": 55112, "runtime": 2076}, {"id": "doc-generator-1755535635814-hd506yjlf", "type": "doc-generator", "status": "active", "tokenUsage": 54369, "runtime": 2094}, {"id": "api-builder-1755535635820-grsxt7ja6", "type": "api-builder", "status": "active", "tokenUsage": 38545, "runtime": 2088}, {"id": "database-architect-1755535635826-per8gwuk8", "type": "database-architect", "status": "active", "tokenUsage": 50293, "runtime": 2082}, {"id": "security-scanner-1755535635831-plhn9dlis", "type": "security-scanner", "status": "active", "tokenUsage": 49091, "runtime": 2077}, {"id": "performance-optimizer-1755535636838-fkaivo2cl", "type": "performance-optimizer", "status": "active", "tokenUsage": 48974, "runtime": 1070}, {"id": "deployment-engineer-1755535636843-qlt7c7ngk", "type": "deployment-engineer", "status": "active", "tokenUsage": 38003, "runtime": 1065}, {"id": "frontend-specialist-1755535636849-7td3av1uv", "type": "frontend-specialist", "status": "active", "tokenUsage": 54786, "runtime": 1059}, {"id": "recovery-specialist-1755535636853-yf8vwje86", "type": "recovery-specialist", "status": "active", "tokenUsage": 44550, "runtime": 1055}, {"id": "code-analyzer-1755535637877-azo16hbb8", "type": "code-analyzer", "status": "active", "tokenUsage": 0, "runtime": 31}, {"id": "test-runner-1755535637890-a58il68z4", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 18}, {"id": "test-runner-1755535637890-u9aypctke", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 17}, {"id": "test-runner-1755535637890-3wmt9sisq", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 17}, {"id": "test-runner-1755535637890-n2g96cs1f", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 17}, {"id": "test-runner-1755535637890-anw1xmoso", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 17}, {"id": "test-runner-1755535637890-h9muz7ifm", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-2jnvewhlf", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-h3vcg6d8r", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-2ykma235a", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-ebu9mjrer", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-d1gwve3fq", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 16}, {"id": "test-runner-1755535637890-x2hax97t0", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 15}, {"id": "test-runner-1755535637890-cl7onsnfe", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 15}, {"id": "test-runner-1755535637890-i6fvnlrxw", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 15}, {"id": "test-runner-1755535637890-axvi96hr6", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 15}, {"id": "test-runner-1755535637890-sbs1n7h0a", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 15}, {"id": "test-runner-1755535637890-0wdi84t56", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 14}, {"id": "test-runner-1755535637890-vffwvgeuz", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 14}, {"id": "test-runner-1755535637890-x60fk6xnz", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 14}, {"id": "test-runner-1755535637890-vbstbjzul", "type": "test-runner", "status": "active", "tokenUsage": 0, "runtime": 14}], "neuralLearning": {"initialized": true, "wasmEnabled": false, "patterns": {"total": 2, "memoryUsage": 0.0002}, "metrics": {"totalWorkflows": 2, "successfulWorkflows": 2, "totalTime": 25762, "averageTime": 12881, "errorRate": 0, "userSatisfaction": 0.778, "resourceEfficiency": 0.5}, "performance": {"predictionsServed": 38, "averagePredictionTime": 0.03833832326194248, "modelAccuracy": 0, "trainingIterations": 0}, "trends": {"trend": "insufficient_data", "confidence": 0}, "trainingQueue": 2, "modelWeights": 4856, "lastSave": 0}}, "metrics": {"agentsSpawned": 31, "tasksDistributed": 30, "tasksCompleted": 1, "averageCompletionTime": 765, "contextUsage": {}, "errors": [{"agentId": "test-runner-1755535635809-48shfub5h", "type": "test-runner", "error": "Simulated agent failure for testing", "timestamp": 1755535637885}]}, "neuralStatus": {"initialized": true, "wasmEnabled": false, "patterns": {"total": 2, "memoryUsage": 0.0002}, "metrics": {"totalWorkflows": 2, "successfulWorkflows": 2, "totalTime": 25762, "averageTime": 12881, "errorRate": 0, "userSatisfaction": 0.778, "resourceEfficiency": 0.5}, "performance": {"predictionsServed": 38, "averagePredictionTime": 0.03833832326194248, "modelAccuracy": 0, "trainingIterations": 0}, "trends": {"trend": "insufficient_data", "confidence": 0}, "trainingQueue": 2, "modelWeights": 4856, "lastSave": 0}}, "testResults": {"total": 188, "byLevel": {"info": 54, "success": 15, "test": 10, "event": 107, "warning": 1, "report": 1}, "errors": [], "warnings": [{"timestamp": "2025-08-18T16:47:15.836Z", "level": "warning", "message": "Scaling warning: Expected 5 agents, got 6", "memory": {"rss": 51535872, "heapTotal": 6529024, "heapUsed": 4594888, "external": 1629158, "arrayBuffers": 77317}}]}, "performance": {"agentSpawnRate": 9.922860025459869, "taskDistributionRate": 9.602767766574066, "memoryPerAgent": 58088.51612903226}}