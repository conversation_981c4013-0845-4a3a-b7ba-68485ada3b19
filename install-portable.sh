#!/bin/bash

# ============================================================================
# MASTER-WORKFLOW Portable Installation Script
# ============================================================================
# Creates a self-contained, portable installation that:
# - Doesn't require root/admin privileges
# - Can be moved/copied to other locations
# - Includes all core components
# - Skips system integrations
# - Provides portable uninstall capability
# ============================================================================

set -euo pipefail

# Color definitions for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly MAGENTA='\033[0;35m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color
readonly BOLD='\033[1m'

# Script configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_NAME="$(basename "$0")"
readonly VERSION="2.0.0"
readonly PORTABLE_DIR_NAME="master-workflow-portable"
readonly MIN_NODE_VERSION="18"

# Default installation target (can be overridden)
INSTALL_TARGET=""
SKIP_NODE_CHECK=false
VERBOSE=false
DRY_RUN=false

# ============================================================================
# Utility Functions
# ============================================================================

print_header() {
    echo -e "${MAGENTA}${BOLD}"
    echo "╔════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     MASTER-WORKFLOW Portable Installer                    ║"
    echo "║                              Version $VERSION                                ║"
    echo "╚════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}[VERBOSE]${NC} $1"
    fi
}

# ============================================================================
# Prerequisites Checking
# ============================================================================

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running as root (not recommended for portable install)
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. Portable installation is designed for user-level deployment."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Installation cancelled by user"
            exit 1
        fi
    fi
    
    # Check Node.js version
    if [[ "$SKIP_NODE_CHECK" == "false" ]]; then
        check_node_version
    fi
    
    # Check required commands
    local required_commands=("npm" "git" "cp" "mkdir" "chmod")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command '$cmd' not found. Please install it first."
            exit 1
        fi
    done
    
    log_success "Prerequisites check completed"
}

check_node_version() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js version $MIN_NODE_VERSION or higher."
        log_info "Download from: https://nodejs.org/"
        exit 1
    fi
    
    local node_version
    node_version=$(node --version | sed 's/v//')
    local node_major_version
    node_major_version=$(echo "$node_version" | cut -d. -f1)
    
    if [[ "$node_major_version" -lt "$MIN_NODE_VERSION" ]]; then
        log_error "Node.js version $node_version is too old. Required: $MIN_NODE_VERSION or higher."
        exit 1
    fi
    
    log_verbose "Node.js version $node_version detected"
}

# ============================================================================
# Installation Target Management
# ============================================================================

determine_install_target() {
    if [[ -z "$INSTALL_TARGET" ]]; then
        # Default to current user's home directory
        INSTALL_TARGET="$HOME/$PORTABLE_DIR_NAME"
    fi
    
    # Expand tilde and resolve path
    INSTALL_TARGET=$(eval echo "$INSTALL_TARGET")
    INSTALL_TARGET=$(realpath -m "$INSTALL_TARGET")
    
    log_info "Installation target: $INSTALL_TARGET"
    
    # Check if target already exists
    if [[ -d "$INSTALL_TARGET" ]]; then
        log_warning "Directory '$INSTALL_TARGET' already exists."
        echo -e "${YELLOW}Options:${NC}"
        echo "  1) Remove existing directory and continue"
        echo "  2) Cancel installation"
        echo "  3) Install to a different location"
        
        read -p "Choose option (1/2/3): " -n 1 -r
        echo
        
        case $REPLY in
            1)
                log_info "Removing existing directory..."
                if [[ "$DRY_RUN" == "false" ]]; then
                    rm -rf "$INSTALL_TARGET"
                fi
                ;;
            2)
                log_info "Installation cancelled by user"
                exit 0
                ;;
            3)
                read -p "Enter new installation path: " new_target
                INSTALL_TARGET=$(eval echo "$new_target")
                INSTALL_TARGET=$(realpath -m "$INSTALL_TARGET")
                determine_install_target  # Recursive call with new target
                return
                ;;
            *)
                log_error "Invalid option. Installation cancelled."
                exit 1
                ;;
        esac
    fi
}

# ============================================================================
# Core Installation Functions
# ============================================================================

create_directory_structure() {
    log_info "Creating directory structure..."
    
    local directories=(
        "$INSTALL_TARGET"
        "$INSTALL_TARGET/bin"
        "$INSTALL_TARGET/lib"
        "$INSTALL_TARGET/config"
        "$INSTALL_TARGET/data"
        "$INSTALL_TARGET/logs"
        "$INSTALL_TARGET/temp"
        "$INSTALL_TARGET/backup"
        "$INSTALL_TARGET/docs"
        "$INSTALL_TARGET/scripts"
        "$INSTALL_TARGET/.ai-workflow"
    )
    
    for dir in "${directories[@]}"; do
        log_verbose "Creating directory: $dir"
        if [[ "$DRY_RUN" == "false" ]]; then
            mkdir -p "$dir"
        fi
    done
    
    log_success "Directory structure created"
}

copy_core_files() {
    log_info "Copying core system files..."
    
    # Define files and directories to copy
    local core_items=(
        "package.json:$INSTALL_TARGET/"
        "README.md:$INSTALL_TARGET/"
        "LICENSE:$INSTALL_TARGET/"
        "CHANGELOG.md:$INSTALL_TARGET/"
        ".ai-workflow:$INSTALL_TARGET/"
        "intelligence-engine:$INSTALL_TARGET/lib/"
        "scripts:$INSTALL_TARGET/"
        "configs:$INSTALL_TARGET/"
        "language-support:$INSTALL_TARGET/lib/"
        "templates:$INSTALL_TARGET/"
        "docs:$INSTALL_TARGET/"
        "tmux-scripts:$INSTALL_TARGET/scripts/"
        "agent-templates:$INSTALL_TARGET/lib/"
    )
    
    for item in "${core_items[@]}"; do
        local source="${item%:*}"
        local dest="${item#*:}"
        local source_path="$SCRIPT_DIR/$source"
        
        if [[ -e "$source_path" ]]; then
            log_verbose "Copying $source to $dest"
            if [[ "$DRY_RUN" == "false" ]]; then
                if [[ -d "$source_path" ]]; then
                    cp -r "$source_path" "$dest"
                else
                    cp "$source_path" "$dest"
                fi
            fi
        else
            log_warning "Source not found: $source_path"
        fi
    done
    
    log_success "Core files copied"
}

install_dependencies() {
    log_info "Installing Node.js dependencies..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_verbose "DRY RUN: Would install npm dependencies"
        return
    fi
    
    # Navigate to installation directory and install dependencies
    cd "$INSTALL_TARGET"
    
    # Create a production-focused package.json if needed
    if [[ -f "package.json" ]]; then
        log_verbose "Installing production dependencies..."
        npm install --production --no-audit --no-fund 2>/dev/null || {
            log_warning "npm install encountered issues, but continuing..."
        }
    fi
    
    # Return to original directory
    cd "$SCRIPT_DIR"
    
    log_success "Dependencies installed"
}

create_launcher_script() {
    log_info "Creating portable launcher script..."
    
    local launcher_path="$INSTALL_TARGET/bin/master-workflow"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$launcher_path" << 'EOF'
#!/bin/bash

# ============================================================================
# MASTER-WORKFLOW Portable Launcher
# ============================================================================
# This script launches the Master Workflow system from its portable location
# ============================================================================

# Determine the portable installation directory
PORTABLE_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
export MASTER_WORKFLOW_ROOT="$PORTABLE_ROOT"
export NODE_PATH="$PORTABLE_ROOT/node_modules:$NODE_PATH"

# Add portable bin directory to PATH for this session
export PATH="$PORTABLE_ROOT/bin:$PATH"

# Set up environment variables
export MASTER_WORKFLOW_PORTABLE=true
export MASTER_WORKFLOW_DATA_DIR="$PORTABLE_ROOT/data"
export MASTER_WORKFLOW_LOG_DIR="$PORTABLE_ROOT/logs"
export MASTER_WORKFLOW_CONFIG_DIR="$PORTABLE_ROOT/config"
export MASTER_WORKFLOW_TEMP_DIR="$PORTABLE_ROOT/temp"

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to print colored output
log_info() {
    echo -e "${BLUE}[MASTER-WORKFLOW]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    log_error "Node.js is not available. Please install Node.js to run Master Workflow."
    exit 1
fi

# Change to the portable root directory
cd "$PORTABLE_ROOT"

# Check for main entry point
if [[ -f "scripts/workflow/workflow-runner.js" ]]; then
    MAIN_SCRIPT="scripts/workflow/workflow-runner.js"
elif [[ -f "workflow-runner.js" ]]; then
    MAIN_SCRIPT="workflow-runner.js"
else
    log_error "Cannot find main workflow runner script"
    exit 1
fi

# Launch the workflow system
log_info "Starting Master Workflow (Portable Mode)"
log_info "Root: $PORTABLE_ROOT"

# Pass all arguments to the main script
exec node "$MAIN_SCRIPT" "$@"
EOF

        chmod +x "$launcher_path"
    fi
    
    log_success "Launcher script created at $launcher_path"
}

create_portable_uninstaller() {
    log_info "Creating portable uninstaller..."
    
    local uninstall_path="$INSTALL_TARGET/bin/uninstall-portable.sh"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$uninstall_path" << 'EOF'
#!/bin/bash

# ============================================================================
# MASTER-WORKFLOW Portable Uninstaller
# ============================================================================
# Safely removes the portable Master Workflow installation
# ============================================================================

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'
BOLD='\033[1m'

# Determine the portable installation directory
PORTABLE_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_header() {
    echo -e "${YELLOW}${BOLD}"
    echo "╔════════════════════════════════════════════════════════════════════════════╗"
    echo "║                   MASTER-WORKFLOW Portable Uninstaller                    ║"
    echo "╚════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

main() {
    print_header
    
    log_info "Portable installation detected at: $PORTABLE_ROOT"
    
    # Check if we're running from within the installation
    if [[ "$PWD" == "$PORTABLE_ROOT"* ]]; then
        log_warning "You are currently inside the installation directory."
        log_info "Please run this script from outside the installation directory."
        
        # Suggest a command to run from parent directory
        local parent_dir
        parent_dir="$(dirname "$PORTABLE_ROOT")"
        echo -e "\n${YELLOW}Suggested command:${NC}"
        echo "cd '$parent_dir' && '$PORTABLE_ROOT/bin/uninstall-portable.sh'"
        exit 1
    fi
    
    # Show what will be removed
    echo -e "\n${YELLOW}The following will be permanently removed:${NC}"
    echo "  📁 $PORTABLE_ROOT"
    echo "  📁 All subdirectories and files"
    echo "  📁 All user data and logs"
    echo "  📁 All configuration files"
    
    # Check for running processes
    local running_processes
    running_processes=$(pgrep -f "master-workflow" || true)
    if [[ -n "$running_processes" ]]; then
        log_warning "Found running Master Workflow processes:"
        ps -p "$running_processes" -o pid,ppid,command || true
        echo
        read -p "Stop these processes and continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Stopping processes..."
            echo "$running_processes" | xargs -r kill -TERM || true
            sleep 2
            # Force kill if still running
            echo "$running_processes" | xargs -r kill -KILL 2>/dev/null || true
        else
            log_info "Please stop the processes manually before uninstalling."
            exit 1
        fi
    fi
    
    # Final confirmation
    echo -e "\n${RED}${BOLD}WARNING: This action cannot be undone!${NC}"
    read -p "Are you absolutely sure you want to remove the portable installation? (yes/NO): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        log_info "Uninstallation cancelled."
        exit 0
    fi
    
    # Create backup of user data if requested
    read -p "Create a backup of user data before removal? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        local backup_dir="$HOME/master-workflow-backup-$(date +%Y%m%d_%H%M%S)"
        log_info "Creating backup at: $backup_dir"
        mkdir -p "$backup_dir"
        
        # Backup important directories
        for dir in "data" "config" "logs"; do
            if [[ -d "$PORTABLE_ROOT/$dir" ]]; then
                cp -r "$PORTABLE_ROOT/$dir" "$backup_dir/" 2>/dev/null || true
            fi
        done
        
        log_success "Backup created at: $backup_dir"
    fi
    
    # Perform the removal
    log_info "Removing portable installation..."
    
    # Remove the installation directory
    if [[ -d "$PORTABLE_ROOT" ]]; then
        rm -rf "$PORTABLE_ROOT"
        log_success "Portable installation removed successfully"
    else
        log_warning "Installation directory not found: $PORTABLE_ROOT"
    fi
    
    echo -e "\n${GREEN}${BOLD}Uninstallation complete!${NC}"
    echo "The Master Workflow portable installation has been removed."
    
    if [[ -n "${backup_dir:-}" ]]; then
        echo "Your data backup is available at: $backup_dir"
    fi
}

# Run main function
main "$@"
EOF

        chmod +x "$uninstall_path"
    fi
    
    log_success "Portable uninstaller created at $uninstall_path"
}

create_configuration_files() {
    log_info "Creating portable configuration files..."
    
    # Create portable configuration
    local config_file="$INSTALL_TARGET/config/portable.conf"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$config_file" << EOF
# ============================================================================
# MASTER-WORKFLOW Portable Configuration
# ============================================================================
# This configuration file is automatically generated for portable installations
# Generated on: $(date)
# Installation path: $INSTALL_TARGET
# ============================================================================

[SYSTEM]
PORTABLE_MODE=true
INSTALLATION_ROOT=$INSTALL_TARGET
DATA_DIRECTORY=\${INSTALLATION_ROOT}/data
LOG_DIRECTORY=\${INSTALLATION_ROOT}/logs
CONFIG_DIRECTORY=\${INSTALLATION_ROOT}/config
TEMP_DIRECTORY=\${INSTALLATION_ROOT}/temp
BACKUP_DIRECTORY=\${INSTALLATION_ROOT}/backup

[FEATURES]
# Core features enabled in portable mode
AGENT_ORCHESTRATION=true
WORKFLOW_MANAGEMENT=true
INTELLIGENCE_ENGINE=true
TMUX_INTEGRATION=true
LOG_MANAGEMENT=true

# System integrations disabled in portable mode
SYSTEM_SERVICE_INTEGRATION=false
GLOBAL_PATH_MODIFICATION=false
SYSTEM_WIDE_CONFIGURATION=false
ROOT_PRIVILEGE_OPERATIONS=false

[SECURITY]
# Security settings for portable mode
RESTRICT_TO_USER_SPACE=true
SANDBOX_MODE=true
DISABLE_SYSTEM_MODIFICATIONS=true

[LOGGING]
LOG_LEVEL=INFO
LOG_ROTATION=true
MAX_LOG_SIZE=10MB
MAX_LOG_FILES=5

[PERFORMANCE]
MEMORY_LIMIT=512MB
CPU_PRIORITY=normal
BACKGROUND_PROCESSES=true
EOF
    fi
    
    # Create environment setup script
    local env_script="$INSTALL_TARGET/bin/setup-env.sh"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$env_script" << 'EOF'
#!/bin/bash
# Master Workflow Portable Environment Setup
# Source this script to set up environment variables

PORTABLE_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

export MASTER_WORKFLOW_ROOT="$PORTABLE_ROOT"
export MASTER_WORKFLOW_PORTABLE=true
export MASTER_WORKFLOW_DATA_DIR="$PORTABLE_ROOT/data"
export MASTER_WORKFLOW_LOG_DIR="$PORTABLE_ROOT/logs"
export MASTER_WORKFLOW_CONFIG_DIR="$PORTABLE_ROOT/config"
export MASTER_WORKFLOW_TEMP_DIR="$PORTABLE_ROOT/temp"
export NODE_PATH="$PORTABLE_ROOT/node_modules:$NODE_PATH"
export PATH="$PORTABLE_ROOT/bin:$PATH"

echo "Master Workflow portable environment configured"
echo "Root: $MASTER_WORKFLOW_ROOT"
EOF

        chmod +x "$env_script"
    fi
    
    log_success "Configuration files created"
}

create_documentation() {
    log_info "Creating portable installation documentation..."
    
    local readme_path="$INSTALL_TARGET/PORTABLE-README.md"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$readme_path" << EOF
# Master Workflow - Portable Installation

This is a portable installation of the Master Workflow system, created on $(date).

## Installation Details

- **Installation Path**: \`$INSTALL_TARGET\`
- **Version**: $VERSION
- **Mode**: Portable (User-space only)
- **Node.js Required**: Version $MIN_NODE_VERSION or higher

## Quick Start

### Starting the System

\`\`\`bash
# Method 1: Use the launcher script
$INSTALL_TARGET/bin/master-workflow

# Method 2: Set up environment and run directly
source $INSTALL_TARGET/bin/setup-env.sh
cd $INSTALL_TARGET
node scripts/workflow/workflow-runner.js
\`\`\`

### Setting Up Environment

To use Master Workflow commands in your current shell session:

\`\`\`bash
source $INSTALL_TARGET/bin/setup-env.sh
\`\`\`

## Directory Structure

\`\`\`
$PORTABLE_DIR_NAME/
├── bin/                    # Executable scripts
│   ├── master-workflow     # Main launcher
│   ├── setup-env.sh       # Environment setup
│   └── uninstall-portable.sh # Uninstaller
├── lib/                    # Core libraries
├── config/                 # Configuration files
├── data/                   # User data
├── logs/                   # System logs
├── temp/                   # Temporary files
├── backup/                 # Backup storage
├── docs/                   # Documentation
└── scripts/                # Utility scripts
\`\`\`

## Features Available in Portable Mode

✅ **Enabled Features:**
- Agent orchestration
- Workflow management
- Intelligence engine
- TMUX integration
- Log management
- Local file operations
- User-space operations

❌ **Disabled Features:**
- System service integration
- Global PATH modifications
- System-wide configuration
- Operations requiring root privileges

## Moving the Installation

This installation is fully portable. To move it:

1. Stop any running Master Workflow processes
2. Copy the entire \`$PORTABLE_DIR_NAME\` directory to the new location
3. Update any shortcuts or scripts that reference the old path
4. Run from the new location

## Backup and Restore

### Creating a Backup
\`\`\`bash
# Manual backup
cp -r $INSTALL_TARGET ~/master-workflow-backup-\$(date +%Y%m%d)

# Or use the built-in backup (if available)
$INSTALL_TARGET/bin/master-workflow --backup
\`\`\`

### Restoring from Backup
\`\`\`bash
# Restore user data
cp -r ~/master-workflow-backup-*/data $INSTALL_TARGET/
cp -r ~/master-workflow-backup-*/config $INSTALL_TARGET/
\`\`\`

## Uninstalling

To completely remove this portable installation:

\`\`\`bash
$INSTALL_TARGET/bin/uninstall-portable.sh
\`\`\`

The uninstaller will:
- Stop running processes
- Offer to create a backup
- Remove the entire installation directory
- Clean up temporary files

## Configuration

The main configuration file is located at:
\`$INSTALL_TARGET/config/portable.conf\`

Edit this file to customize the behavior of your portable installation.

## Troubleshooting

### Common Issues

1. **"Node.js not found" error**
   - Ensure Node.js version $MIN_NODE_VERSION+ is installed
   - Check that \`node\` and \`npm\` are in your PATH

2. **Permission denied errors**
   - Ensure the installation directory is writable
   - Check that scripts have execute permissions

3. **Port conflicts**
   - The system may use various ports for internal communication
   - Check the logs in \`$INSTALL_TARGET/logs/\` for details

### Log Files

System logs are stored in:
- \`$INSTALL_TARGET/logs/system/\` - System logs
- \`$INSTALL_TARGET/logs/agents/\` - Agent logs
- \`$INSTALL_TARGET/logs/workflows/\` - Workflow logs

### Getting Help

1. Check the main documentation in \`$INSTALL_TARGET/docs/\`
2. Review log files for error messages
3. Ensure all prerequisites are met
4. Try running with verbose output: \`--verbose\` flag

## Security Considerations

This portable installation runs in user-space only and includes these security measures:

- No system-wide modifications
- Restricted to user permissions
- Sandboxed operation mode
- No root privilege requirements
- Local file system access only

## Performance Optimization

For better performance:

1. Ensure adequate disk space (recommended: 1GB free)
2. Close unnecessary applications to free memory
3. Use SSD storage if available
4. Keep the installation on the local drive (not network)

## Updates

To update this portable installation:

1. Download the latest portable installer
2. Create a backup of your current data
3. Run the new installer with the same target directory
4. Restore your data and configuration

---

**Installation completed on**: $(date)
**Installer version**: $VERSION
**Installation type**: Portable
EOF
    fi
    
    log_success "Documentation created at $readme_path"
}

# ============================================================================
# Main Installation Process
# ============================================================================

perform_installation() {
    log_info "Starting portable installation process..."
    
    # Step 1: Create directory structure
    create_directory_structure
    
    # Step 2: Copy core files
    copy_core_files
    
    # Step 3: Install dependencies
    install_dependencies
    
    # Step 4: Create launcher script
    create_launcher_script
    
    # Step 5: Create uninstaller
    create_portable_uninstaller
    
    # Step 6: Create configuration
    create_configuration_files
    
    # Step 7: Create documentation
    create_documentation
    
    log_success "Portable installation completed successfully!"
}

print_installation_summary() {
    echo
    echo -e "${GREEN}${BOLD}╔════════════════════════════════════════════════════════════════════════════╗"
    echo -e "║                          Installation Complete!                           ║"
    echo -e "╚════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${WHITE}📁 Installation Location:${NC} $INSTALL_TARGET"
    echo -e "${WHITE}🚀 Launcher Script:${NC} $INSTALL_TARGET/bin/master-workflow"
    echo -e "${WHITE}📖 Documentation:${NC} $INSTALL_TARGET/PORTABLE-README.md"
    echo -e "${WHITE}🗑️  Uninstaller:${NC} $INSTALL_TARGET/bin/uninstall-portable.sh"
    echo
    echo -e "${CYAN}Quick Start Commands:${NC}"
    echo -e "  ${YELLOW}# Start Master Workflow${NC}"
    echo -e "  $INSTALL_TARGET/bin/master-workflow"
    echo
    echo -e "  ${YELLOW}# Set up environment for current shell${NC}"
    echo -e "  source $INSTALL_TARGET/bin/setup-env.sh"
    echo
    echo -e "  ${YELLOW}# View documentation${NC}"
    echo -e "  cat $INSTALL_TARGET/PORTABLE-README.md"
    echo
    echo -e "${GREEN}The installation is fully portable and can be moved to any location.${NC}"
    echo -e "${GREEN}No system-wide changes were made.${NC}"
    echo
}

# ============================================================================
# Command Line Argument Parsing
# ============================================================================

show_help() {
    cat << EOF
Master Workflow Portable Installer v$VERSION

USAGE:
    $SCRIPT_NAME [OPTIONS] [INSTALL_PATH]

DESCRIPTION:
    Creates a self-contained, portable installation of Master Workflow that:
    - Doesn't require root/admin privileges
    - Can be moved/copied to other locations  
    - Includes all core components
    - Skips system integrations
    - Provides portable uninstall capability

OPTIONS:
    -h, --help              Show this help message
    -v, --verbose           Enable verbose output
    -t, --target PATH       Specify installation target directory
    --skip-node-check       Skip Node.js version checking
    --dry-run              Show what would be done without making changes
    --version              Show version information

EXAMPLES:
    # Install to default location (~/$PORTABLE_DIR_NAME)
    $SCRIPT_NAME

    # Install to specific directory
    $SCRIPT_NAME --target /opt/master-workflow

    # Install with verbose output
    $SCRIPT_NAME --verbose --target ~/my-ai-tools/master-workflow

    # Dry run to see what would be installed
    $SCRIPT_NAME --dry-run

REQUIREMENTS:
    - Node.js version $MIN_NODE_VERSION or higher
    - npm package manager
    - Basic Unix tools (cp, mkdir, chmod)
    - At least 100MB free disk space

For more information, visit: https://github.com/your-org/master-workflow
EOF
}

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -t|--target)
                INSTALL_TARGET="$2"
                shift 2
                ;;
            --skip-node-check)
                SKIP_NODE_CHECK=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                VERBOSE=true
                shift
                ;;
            --version)
                echo "Master Workflow Portable Installer v$VERSION"
                exit 0
                ;;
            -*)
                log_error "Unknown option: $1"
                echo "Use --help for usage information."
                exit 1
                ;;
            *)
                # Positional argument - treat as install target
                if [[ -z "$INSTALL_TARGET" ]]; then
                    INSTALL_TARGET="$1"
                else
                    log_error "Multiple install targets specified"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# ============================================================================
# Main Execution
# ============================================================================

main() {
    # Handle command line arguments
    parse_arguments "$@"
    
    # Show header
    print_header
    
    # Show dry run notice
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${YELLOW}${BOLD}DRY RUN MODE - No changes will be made${NC}\n"
    fi
    
    # Check prerequisites
    check_prerequisites
    
    # Determine installation target
    determine_install_target
    
    # Perform the installation
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would perform installation to $INSTALL_TARGET"
        log_info "DRY RUN: All prerequisites satisfied"
    else
        perform_installation
        print_installation_summary
    fi
}

# Execute main function with all arguments
main "$@"