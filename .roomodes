{"modes": {"default": {"name": "Default SPARC Mode", "description": "Standard SPARC methodology", "phases": ["Specification", "Pseudocode", "Architecture", "Refinement", "Completion"]}, "rapid": {"name": "Rapid Development", "description": "Fast-track development with essential phases", "phases": ["Specification", "Architecture", "Completion"]}, "enterprise": {"name": "Enterprise SPARC", "description": "Full enterprise methodology with documentation", "phases": ["Specification", "Pseudocode", "Architecture", "Refinement", "Testing", "Documentation", "Completion"]}}, "active": "default", "version": "2.0", "created": "2025-08-18", "description": "SPARC methodology configuration for MASTER-WORKFLOW"}