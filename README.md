# MASTER-WORKFLOW

![Version](https://img.shields.io/badge/version-2.0-blue)
![License](https://img.shields.io/badge/license-MIT-green)
![Node](https://img.shields.io/badge/node-%3E%3D18-brightgreen)
![Platform](https://img.shields.io/badge/platform-Linux%20%7C%20macOS%20%7C%20Windows-lightgrey)

**An intelligent, modular workflow orchestration system that adapts to your project's complexity and automatically selects the optimal development approach.**

MASTER-WORKFLOW combines AI-powered project analysis with flexible component architecture to provide everything from simple automation to enterprise-grade multi-agent coordination. Whether you're building a quick prototype or managing a complex enterprise system, MASTER-WORKFLOW intelligently scales to meet your needs.

## ✨ Key Features

### 🧠 Intelligent Analysis & Adaptation
- **Smart Complexity Analysis** - Analyzes projects across 8 dimensions (size, dependencies, architecture, tech stack, features, team size, deployment, testing)
- **Automatic Approach Selection** - Chooses optimal workflow from Simple Swarm to Enterprise SPARC methodology
- **Tech Stack Detection** - Identifies languages, frameworks, and patterns to customize documentation and workflows
- **Lifecycle Awareness** - Adapts behavior based on project maturity (idea, early, active, mature stages)

### 🎛️ Modular Architecture
- **Component Selection** - Install only what you need: Core, Claude Code, Agent-OS, Claude Flow 2.0, TMux Orchestrator
- **Progressive Enhancement** - Add components as your project grows
- **Cross-Platform Support** - Works on Linux, macOS, Windows (including WSL)
- **Multiple Installation Options** - Interactive, production, standalone, AI-Dev-OS integration, containerized

### 🤖 Multi-Agent Coordination
- **Hierarchical Agent Management** - Queen Controller manages up to 23 specialized sub-agents
- **Intelligent Workflow Selection** - Simple Swarm (0-30 complexity), Hive-Mind (31-70), SPARC + Hive-Mind (71-100)
- **Neural Learning Capabilities** - Agents learn and improve from project interactions
- **200k Context Windows** - Each agent maintains extensive context for deep understanding

### 🔄 Flexible Execution Modes
- **TMux Integration** - 24/7 autonomous operation with session management
- **Fallback Modes** - Works with or without TMux for maximum compatibility
- **Interactive & Automated** - Choose between guided setup or full automation
- **Recovery Systems** - Comprehensive error handling, checkpoints, and backup points

### 📝 Documentation & Configuration
- **Auto-Generated Documentation** - Creates project-specific guides and API documentation
- **Customizable Templates** - 100+ MCP server configurations for different tech stacks
- **Multi-Format Export** - Markdown, HTML, PDF, and interactive documentation
- **Configuration Management** - Layered configuration system from global to project-specific

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/MASTER-WORKFLOW.git
cd MASTER-WORKFLOW
```

### 2. Navigate to Your Project
```bash
cd /path/to/your/project
```

### 3. Install MASTER-WORKFLOW
```bash
# Interactive installation (recommended)
/path/to/MASTER-WORKFLOW/scripts/installation/install-modular.sh

# Production installation (all components)
/path/to/MASTER-WORKFLOW/scripts/installation/install-production.sh

# Standalone installation (core only)
/path/to/MASTER-WORKFLOW/scripts/installation/install-standalone.sh
```

### 4. Start Working
```bash
# Analyze your project and auto-select approach
./ai-workflow init --auto "Build a REST API with authentication"

# Use interactive mode for guided setup
./ai-workflow init

# Force specific approaches
./ai-workflow init --swarm "Quick bug fix"
./ai-workflow init --hive "Feature development"
./ai-workflow init --sparc "Enterprise project"
```

## 📋 Installation Methods

Choose the installation method that best fits your needs:

### 1. Interactive Modular (Recommended)
Perfect for choosing specific components during installation:

```bash
/path/to/MASTER-WORKFLOW/scripts/installation/install-modular.sh
```

**Component Options:**
- ✅ **Core Workflow** - Intelligence engine, complexity analysis (required)
- ⭕ **Claude Code Integration** - AI-powered agents and automation hooks
- ⭕ **Agent-OS Planning** - Specification-driven development
- ⭕ **Claude Flow 2.0** - Multi-agent coordination (Swarm/Hive-Mind)
- ⭕ **TMux Orchestrator** - 24/7 autonomous operation

### 2. Production Installation
Install all components for maximum functionality:

```bash
/path/to/MASTER-WORKFLOW/scripts/installation/install-production.sh
```

**Best for:** Production environments, team collaboration, complex projects

### 3. AI-Dev-OS Integration
Integrate with AI Development Operating System:

```bash
/path/to/MASTER-WORKFLOW/scripts/installation/install-ai-dev-os.sh
```

**Features:** System-wide installation, global CLI access, cross-project configuration

### 4. Standalone Installation
Minimal installation with core functionality only:

```bash
/path/to/MASTER-WORKFLOW/scripts/installation/install-standalone.sh
```

**Best for:** Quick prototyping, learning the system, minimal resource usage

### 5. Containerized Installation
Deploy using Docker for isolated environments:

```dockerfile
FROM node:18-alpine
RUN apk add --no-cache git tmux bash
WORKDIR /app
COPY . /master-workflow
RUN cd /master-workflow && npm install
WORKDIR /workspace
COPY your-project/ .
RUN /master-workflow/scripts/installation/install-production.sh
ENTRYPOINT ["./ai-workflow"]
```

### 6. Portable Installation
Self-contained installation that requires no root privileges, perfect for testing environments:

```bash
/path/to/MASTER-WORKFLOW/install-portable.sh
```

**Features:**
- No root privileges required
- Self-contained with all dependencies
- Perfect for testing and evaluation
- Portable across different systems
- Self-contained installer available in repository root

**Best for:** Testing environments, restricted systems, quick evaluations, shared hosting

## 🗑️ Uninstallation

MASTER-WORKFLOW provides comprehensive uninstallation options with built-in safety features to ensure clean removal while protecting your project data.

### Smart Uninstaller Features

- **Backup Creation** - Automatically backs up configurations before removal
- **Dry-run Mode** - Preview what will be removed without making changes
- **Interactive UI** - Guided removal with component selection
- **Selective Removal** - Remove specific components while keeping others
- **Manual Cleanup** - Fallback procedures for complete removal

### Basic Uninstall Commands

```bash
# Interactive uninstallation (recommended)
./.ai-workflow/bin/uninstall.sh

# Windows PowerShell
.\.ai-workflow\bin\uninstall.ps1

# Dry-run to preview changes
./.ai-workflow/bin/uninstall.sh --dry-run

# Silent uninstall (all components)
./.ai-workflow/bin/uninstall.sh --silent --all
```

### Examples

#### Safe Preview (Dry-run)
```bash
# See what would be removed without making changes
./.ai-workflow/bin/uninstall.sh --dry-run

# Output preview:
# Would remove:
# - .ai-workflow/ directory (127 MB)
# - ai-workflow symlink
# - .ai-dev/ configuration (preserving analysis.json)
# - .claude/ integration files
# Backup would be created at: ~/.ai-workflow-backups/project-backup-2024-01-15/
```

#### Complete Uninstall with Backup
```bash
# Remove all components with automatic backup
./.ai-workflow/bin/uninstall.sh --backup

# Backup location will be displayed:
# Backup created: ~/.ai-workflow-backups/my-project-20240115-143022/
# Components removed: core, claude-code, agent-os, claude-flow, tmux
# Uninstallation completed successfully
```

#### Selective Component Removal
```bash
# Remove specific components only
./.ai-workflow/bin/uninstall.sh --components claude-flow,tmux

# Interactive component selection
./.ai-workflow/bin/uninstall.sh --interactive

# Keep core but remove integrations
./.ai-workflow/bin/uninstall.sh --keep-core --remove-integrations
```

### Manual Cleanup Procedures

If the automated uninstaller fails or you need complete manual removal:

```bash
# 1. Stop any running sessions
tmux kill-session -t ai-workflow 2>/dev/null

# 2. Remove main installation directory
rm -rf .ai-workflow/

# 3. Remove project metadata
rm -rf .ai-dev/

# 4. Remove integration directories (if present)
rm -rf .claude/
rm -rf .agent-os/
rm -rf .claude-flow/

# 5. Remove symlinks and executables
rm -f ai-workflow
rm -f .ai-workflow-cli

# 6. Remove configuration files
rm -f .mcp-config.json
rm -f .ai-workflow-config.json

# 7. Clean up environment variables (add to ~/.bashrc or ~/.zshrc)
unset CLAUDE_FLOW_VERSION
unset AI_WORKFLOW_MODE
unset TMUX_SESSION_NAME
```

### Backup Recovery

If you need to restore from a backup:

```bash
# List available backups
./.ai-workflow/bin/uninstall.sh --list-backups

# Restore from specific backup
./.ai-workflow/bin/uninstall.sh --restore ~/.ai-workflow-backups/project-20240115/

# Restore latest backup
./.ai-workflow/bin/uninstall.sh --restore-latest
```

### Verification

Confirm complete removal:

```bash
# Check for remaining files
find . -name "*ai-workflow*" -o -name ".ai-dev" -o -name ".claude"

# Verify no running processes
ps aux | grep ai-workflow

# Check for environment variables
env | grep -i workflow
```

For additional help with uninstallation, see the [Troubleshooting Guide](documentation/root-docs/how-to-run-master-workflow.md) or refer to the uninstaller scripts at:
- `.ai-workflow/bin/uninstall.sh` (Linux/macOS)
- `.ai-workflow/bin/uninstall.ps1` (Windows PowerShell)

## 🎮 How to Use MASTER-WORKFLOW

### Project Analysis
MASTER-WORKFLOW automatically analyzes your codebase across 8 complexity dimensions:

```bash
# Analyze project complexity
./ai-workflow analyze

# View analysis results
./ai-workflow status

# Force re-analysis
./ai-workflow analyze --force
```

**Analysis Dimensions:**
- **Size** - Files, lines of code, project scale
- **Dependencies** - Package complexity, external libraries
- **Architecture** - Monolith vs microservices, patterns
- **Tech Stack** - Languages, frameworks, databases
- **Features** - Authentication, real-time, APIs, etc.
- **Team Size** - Collaboration indicators, multi-developer patterns
- **Deployment** - Containers, cloud, infrastructure complexity
- **Testing** - Coverage, frameworks, test complexity

### Workflow Approaches

Based on complexity score (0-100), MASTER-WORKFLOW automatically selects the optimal approach:

| Score Range | Approach | Description | Best For |
|-------------|----------|-------------|----------|
| **0-30** | Simple Swarm | Single agent coordination | Quick tasks, bug fixes, simple features |
| **31-70** | Hive-Mind | Multi-agent collaboration | Feature development, moderate complexity |
| **71-100** | SPARC + Hive-Mind | Enterprise methodology | Large projects, complex systems |

### Command Examples

```bash
# Automatic approach selection
./ai-workflow init --auto "Create a mobile app with user authentication"

# Manual approach selection
./ai-workflow init --swarm "Fix login validation bug"
./ai-workflow init --hive "Add user profile management"
./ai-workflow init --sparc "Build enterprise e-commerce platform"

# Interactive mode with guided setup
./ai-workflow init

# Component management
./ai-workflow components                    # List installed components
./ai-workflow add claude-flow              # Add component
./ai-workflow remove tmux                  # Remove component
./ai-workflow update                       # Update all components
```

### Session Management

```bash
# TMux integration (if installed)
./ai-workflow session create "project-name"
./ai-workflow session list
./ai-workflow session attach "project-name"

# Background execution
./ai-workflow init --background "Long-running task"

# Monitor progress
./ai-workflow status-dashboard 8787        # Web dashboard on port 8787
```

## 🏗️ System Architecture

MASTER-WORKFLOW uses a modular, hierarchical architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    MASTER-WORKFLOW SYSTEM                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌──────────────────┐  ┌─────────────┐  │
│  │   Intelligence  │  │    Component     │  │ Integration │  │
│  │     Engine      │◄─┤    Manager       │─►│    Layer    │  │
│  └─────────────────┘  └──────────────────┘  └─────────────┘  │
│           │                      │                    │      │
│  ┌─────────────────┐  ┌──────────────────┐  ┌─────────────┐  │
│  │   Workflow      │  │     Session      │  │     MCP     │  │
│  │  Orchestrator   │  │    Manager       │  │  Protocol   │  │
│  └─────────────────┘  └──────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

- **Intelligence Engine** - Project analysis, approach selection, tech stack detection
- **Component Manager** - Modular installation, dependency resolution, updates
- **Workflow Orchestrator** - Task coordination, agent management, execution
- **Session Manager** - TMux integration, process management, recovery
- **Integration Layer** - External systems, MCP protocol, API connections

### Project Structure

After installation, your project will have:

```
your-project/
├── .ai-workflow/           # Local installation
│   ├── bin/ai-workflow     # CLI interface
│   ├── intelligence-engine/ # Analysis engines
│   ├── configs/            # System configuration
│   └── templates/          # Workflow templates
├── .ai-dev/               # Project metadata
│   ├── analysis.json      # Complexity analysis
│   ├── approach.json      # Selected approach
│   └── config.json        # Project settings
├── .claude/               # Claude Code integration (optional)
├── .agent-os/             # Agent-OS specifications (optional)
├── .claude-flow/          # Claude Flow configuration (optional)
└── ai-workflow            # CLI command (symlink)
```

## 📚 Documentation

Comprehensive documentation is organized in the `/documentation/` directory:

### Quick Start Guides
- **[Getting Started](documentation/tutorials/tutorial-01-getting-started.md)** - First steps with MASTER-WORKFLOW
- **[Installation Guide](documentation/root-docs/INSTALLATION.md)** - Detailed setup instructions for all methods
- **[Configuration Guide](documentation/root-docs/CONFIGURATION.md)** - Complete configuration reference
- **[User Guide](docs/USER-GUIDE-COMPLETE.md)** - Step-by-step usage guide

### Core System Documentation
- **[System Architecture](documentation/root-docs/ARCHITECTURE.md)** - Complete system design and component relationships
- **[API Reference](documentation/api/)** - System APIs, endpoints, and integration points
- **[Intelligence Engine](documentation/intelligence-engine/)** - AI analysis components and complexity assessment
- **[Workflow System](documentation/workflows/)** - Workflow methodologies and orchestration

### Agent System
- **[Agents Overview](documentation/agents/)** - Complete agent system documentation
- **[Agent Development](documentation/agents/agent-development.md)** - Creating and customizing agents
- **[Multi-Agent Coordination](documentation/tutorials/tutorial-04-multi-agent-workflows.md)** - Advanced agent coordination

### Integration & Configuration
- **[MCP Servers](documentation/mcp-servers/)** - Model Context Protocol integration and server configuration
- **[Technology Support](language-support/)** - Multi-language development support
- **[TMux Orchestration](documentation/tutorials/tutorial-05-tmux-orchestration.md)** - 24/7 autonomous operation

### Performance & Security
- **[Performance Guide](documentation/performance/)** - Optimization, scaling, and monitoring
- **[Security Guide](documentation/security/)** - Security guidelines, authentication, and compliance
- **[Testing Reports](documentation/testing-reports/)** - Validation and quality assurance

### Migration & Maintenance
- **[Migration Guide](documentation/migration/)** - Upgrade procedures and data migration
- **[Command Reference](documentation/root-docs/MASTER-COMMANDS-REFERENCE.md)** - Complete command reference
- **[Troubleshooting](documentation/root-docs/how-to-run-master-workflow.md)** - Common issues and solutions

### Examples & Tutorials
- **[Demo Workflows](docs/DEMO-WORKFLOWS.md)** - Real-world examples and scenarios
- **[Use Cases](documentation/examples/)** - Practical applications and step-by-step tutorials
- **[Tutorial Series](documentation/tutorials/)** - Progressive learning guides
- **[Contributing Guide](docs/CONTRIBUTING.md)** - Development and contribution guidelines

## 🎯 Features Explained

### Intelligent Project Analysis
- **Multi-Dimensional Scoring** - Evaluates 8 complexity factors with weighted algorithms
- **Technology Detection** - Identifies languages, frameworks, patterns, and architecture
- **Lifecycle Assessment** - Determines project maturity and adapts workflows accordingly
- **Team Collaboration Analysis** - Detects multi-developer patterns and coordination needs

### Adaptive Workflow Selection
- **Complexity-Based Routing** - Automatically selects optimal approach based on analysis
- **Manual Override** - Force specific approaches when needed
- **Hybrid Approaches** - Combines methodologies for complex scenarios
- **Progressive Enhancement** - Workflows can evolve as projects grow

### Multi-Agent Coordination
- **Queen Controller** - Hierarchical management of specialized sub-agents
- **Specialized Agents** - 23 different agent types for specific tasks
- **Memory Management** - Persistent memory across sessions and agents
- **Communication Protocols** - Structured inter-agent communication

### Enterprise Features
- **SPARC Methodology** - Specification, Pseudocode, Architecture, Refinement, Completion
- **Enterprise Templates** - Pre-configured setups for common enterprise patterns
- **Compliance Support** - Built-in security scanning and compliance checking
- **Scaling Support** - Horizontal and vertical scaling capabilities

## 🔧 Configuration

MASTER-WORKFLOW uses a layered configuration system:

### Environment Variables
```bash
export CLAUDE_FLOW_VERSION=stable        # Claude Flow version
export AI_WORKFLOW_MODE=interactive      # Execution mode
export TMUX_SESSION_NAME=ai-workflow     # TMux session name
export LOG_LEVEL=info                    # Logging level
export NODE_ENV=production               # Environment mode
```

### Project Configuration
```json
// .ai-dev/config.json
{
  "projectName": "my-project",
  "complexity": 65,
  "approach": "hive-mind",
  "components": ["core", "claude-code", "claude-flow"],
  "preferences": {
    "autostart": true,
    "tmux": true,
    "interactive": false
  }
}
```

### Component Configuration
- **Claude Code**: `.claude/settings.json`
- **Agent-OS**: `.agent-os/config.yaml`
- **Claude Flow**: `.claude-flow/hive-config.json`
- **MCP Servers**: `.mcp-config.json`

## 🧪 Troubleshooting

### Common Issues

#### Installation Fails
```bash
# Fix permissions
chmod +x /path/to/MASTER-WORKFLOW/install-*.sh

# Run with explicit bash
bash /path/to/MASTER-WORKFLOW/scripts/installation/install-modular.sh

# Check system requirements
node --version  # Should be 18+
npm --version   # Should be working
git --version   # Should be available

# Clear previous failed installation
rm -rf .ai-workflow/ .ai-dev/ .claude/ .agent-os/
```

#### Node.js Version Issues
```bash
# Update Node.js using nvm
nvm install 18 && nvm use 18

# Or using package manager (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version && npm --version
```

#### TMux Not Found
```bash
# Install tmux (Ubuntu/Debian)
sudo apt-get install tmux

# Install tmux (macOS)
brew install tmux

# Install tmux (CentOS/RHEL)
sudo yum install tmux

# Or configure without tmux
./ai-workflow configure --no-tmux
```

#### Command Not Found
```bash
# Check symlink
ls -la ai-workflow

# Recreate if missing
ln -sf .ai-workflow/bin/ai-workflow ai-workflow

# Use absolute path
./.ai-workflow/bin/ai-workflow --help

# Check PATH variable
echo $PATH | grep -q "$(pwd)" || export PATH="$PATH:$(pwd)"
```

#### Portable Installation Issues

**Issue: Installation fails on network drives or USB storage**
```bash
# Use local temp directory
export TMPDIR=/tmp/ai-workflow-install
mkdir -p $TMPDIR

# Install with explicit temp directory
TMPDIR=$TMPDIR ./install-modular.sh

# Or use standalone installation for portable use
./install-standalone.sh --portable
```

**Issue: Permissions on shared drives**
```bash
# Check file system support
mount | grep "$(df . | tail -1 | awk '{print $1}')"

# For network drives, use local installation
mkdir -p ~/ai-workflow-portable
cd ~/ai-workflow-portable
/path/to/MASTER-WORKFLOW/scripts/installation/install-standalone.sh
```

#### Component Integration Failures
```bash
# Verify all components
./ai-workflow verify

# Check component status
./ai-workflow components

# Reinstall specific component
./ai-workflow remove claude-flow
./ai-workflow add claude-flow

# Check integration logs
tail -f .ai-workflow/logs/integration.log
```

### Uninstallation Issues

#### Uninstaller Cannot Start
```bash
# Check if uninstaller exists
ls -la .ai-workflow/bin/uninstall.sh

# Make executable if needed
chmod +x .ai-workflow/bin/uninstall.sh

# Run with explicit interpreter
bash .ai-workflow/bin/uninstall.sh

# Use fallback uninstaller
curl -s https://raw.githubusercontent.com/your-org/MASTER-WORKFLOW/main/scripts/emergency-uninstall.sh | bash
```

#### Partial Uninstall Recovery
```bash
# If uninstaller was interrupted
./.ai-workflow/bin/uninstall.sh --resume

# Or complete manual cleanup
rm -rf .ai-workflow/
rm -rf .ai-dev/
rm -rf .claude/
rm -rf .agent-os/
rm -f ai-workflow .ai-workflow-cli

# Clean up running processes
pkill -f "ai-workflow"
tmux kill-session -t ai-workflow 2>/dev/null || true
```

#### Manual Cleanup Procedures

**Complete Manual Removal:**
```bash
#!/bin/bash
# manual-cleanup.sh - Use when automated uninstaller fails

echo "Starting manual cleanup..."

# Stop any running sessions
tmux kill-session -t ai-workflow 2>/dev/null || true
tmux kill-session -t queen-agent-* 2>/dev/null || true

# Stop processes
pkill -f "ai-workflow" 2>/dev/null || true
pkill -f "workflow-engine" 2>/dev/null || true
pkill -f "queen-controller" 2>/dev/null || true

# Remove main installation directory
if [[ -d ".ai-workflow" ]]; then
    echo "Removing .ai-workflow directory..."
    rm -rf .ai-workflow/
fi

# Remove project metadata (keep analysis.json by default)
if [[ -d ".ai-dev" ]]; then
    echo "Backing up analysis results..."
    cp .ai-dev/analysis.json ./ai-workflow-analysis-backup.json 2>/dev/null || true
    rm -rf .ai-dev/
fi

# Remove integration directories
echo "Removing integration directories..."
rm -rf .claude/ .agent-os/ .claude-flow/

# Remove symlinks and executables
rm -f ai-workflow .ai-workflow-cli

# Remove configuration files
rm -f .mcp-config.json .ai-workflow-config.json

# Clean up environment variables
unset CLAUDE_FLOW_VERSION AI_WORKFLOW_MODE TMUX_SESSION_NAME

echo "Manual cleanup completed"
echo "Analysis backup saved as: ai-workflow-analysis-backup.json"
```

#### Backup Recovery
```bash
# List available backups
ls -la ~/.ai-workflow-uninstall-backups/

# Restore from backup
backup_dir="~/.ai-workflow-uninstall-backups/project-20240115/"
if [[ -d "$backup_dir" ]]; then
    echo "Restoring from: $backup_dir"
    cp -r "$backup_dir"/* ./
    echo "Restore completed"
else
    echo "Backup not found: $backup_dir"
fi

# Verify restored installation
./ai-workflow verify
```

#### Permission Issues During Uninstall
```bash
# Fix ownership before uninstall
sudo chown -R $(whoami):$(id -gn) .ai-workflow/

# Or force removal with sudo (use carefully)
sudo rm -rf .ai-workflow/

# Clean up any remaining processes owned by root
sudo pkill -f "ai-workflow" 2>/dev/null || true
```

#### Files Won't Delete (Windows)
```powershell
# PowerShell script for Windows cleanup
Stop-Process -Name "*workflow*" -Force -ErrorAction SilentlyContinue

# Remove directories (Windows)
Remove-Item -Recurse -Force ".ai-workflow" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force ".ai-dev" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force ".claude" -ErrorAction SilentlyContinue

# Handle locked files
Get-Process | Where-Object {$_.ProcessName -like "*workflow*"} | Stop-Process -Force

# Use Windows uninstaller
.\.ai-workflow\bin\uninstall.ps1
```

### Debug Mode
```bash
# Enable detailed logging
DEBUG=* ./ai-workflow analyze

# Check logs
tail -f .ai-workflow/logs/installation.log
tail -f .ai-workflow/logs/runtime.log
tail -f .ai-workflow/logs/uninstall.log

# Run comprehensive verification
./ai-workflow verify --detailed

# Health check
./ai-workflow health

# Generate diagnostic report
./ai-workflow diagnose > diagnostic-report.txt
```

### Recovery from Corrupted Installation
```bash
# Detect corruption
./ai-workflow verify --fix

# Repair installation
./ai-workflow repair

# Emergency reset (preserves user data)
./ai-workflow emergency-reset

# Complete reinstallation
rm -rf .ai-workflow/
./install-modular.sh --restore-settings
```

### Performance Issues
```bash
# Check resource usage
./ai-workflow status --resources

# Optimize performance
./ai-workflow optimize

# Clean up logs and cache
./ai-workflow cleanup --logs --cache

# Monitor system resources
./ai-workflow monitor --duration 60
```

### Getting Help
1. **Check logs**: `.ai-workflow/logs/`
2. **Run verification**: `./ai-workflow verify`
3. **Generate diagnostic**: `./ai-workflow diagnose`
4. **Try safe mode**: `./ai-workflow init --safe-mode`
5. **Emergency recovery**: `./ai-workflow emergency-reset`
6. **Manual cleanup**: Use scripts above for manual removal
7. **Report issues**: [GitHub Issues](https://github.com/your-org/MASTER-WORKFLOW/issues)

### Advanced Troubleshooting
For complex issues, see the comprehensive troubleshooting guides:
- **[Migration Issues](documentation/migration/troubleshooting-migration.md)** - Migration and upgrade problems
- **[Installation Guide](documentation/root-docs/INSTALLATION.md)** - Detailed installation troubleshooting
- **[Configuration Guide](documentation/root-docs/CONFIGURATION.md)** - Configuration and setup issues
- **[Performance Guide](documentation/performance/optimization-guide.md)** - Performance optimization

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/CONTRIBUTING.md) for:

- Development setup and environment
- Code standards and style guidelines
- Testing requirements and procedures
- Pull request process and review

### Development Setup
```bash
# Clone repository
git clone https://github.com/your-org/MASTER-WORKFLOW.git
cd MASTER-WORKFLOW

# Install dependencies
npm install

# Run tests
npm test

# Start development mode
npm run dev
```

### Code Standards
- **ESLint** - Code linting and style enforcement
- **Prettier** - Code formatting
- **Jest** - Unit and integration testing
- **Conventional Commits** - Commit message format

## 📊 Requirements

### System Requirements
- **Node.js 18+** - JavaScript runtime for intelligence engine
- **npm** - Package management and dependencies
- **git** - Version control (recommended for full functionality)
- **tmux** - Session management (optional, Linux/macOS/WSL)

### Platform Requirements
- **Memory**: 2GB RAM minimum (4GB recommended)
- **Storage**: 500MB free space (1GB+ for full ecosystem)
- **Network**: Internet connection for MCP servers and updates

### Supported Platforms
- **Linux** - Ubuntu 18.04+, CentOS 7+, Debian 9+
- **macOS** - 10.14+ (Mojave and later)
- **Windows** - Windows 10/11 with PowerShell 5.1+
- **WSL** - Windows Subsystem for Linux v2

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Third-Party Components
MASTER-WORKFLOW incorporates several open-source technologies:
- **Claude Code CLI** by Anthropic (enhanced with custom orchestration)
- **Multi-Agent Frameworks** - Claude Flow 2.0, Agent-OS integration
- **Node.js Ecosystem** - Various npm packages (see package.json)
- **Development Tools** - Cross-platform compatibility libraries

All third-party components maintain their original licenses.

## 🏆 Features Status

- ✅ **Intelligent project analysis** - Multi-dimensional complexity scoring
- ✅ **Modular component architecture** - Progressive enhancement
- ✅ **Multi-agent coordination** - Swarm, Hive-Mind, SPARC methodologies  
- ✅ **Tech stack detection** - Automatic language and framework identification
- ✅ **Cross-platform support** - Linux, macOS, Windows compatibility
- ✅ **TMux integration** - 24/7 autonomous operation with fallback modes
- ✅ **Configuration management** - Layered configuration system
- ✅ **Production-ready** - Comprehensive error handling and logging
- ✅ **Documentation generation** - Auto-generated project documentation
- ✅ **Recovery systems** - Checkpoints, backups, and error recovery

---

## 👨‍💻 Author

**Designed and Built by Beau Lewis**
- Email: <EMAIL>
- Passion: Creating applications that help people solve real problems

## 💝 Support & Donations

I believe in creating applications that genuinely help people, even if it requires significant time and resources. If MASTER-WORKFLOW has helped you or your team, and you'd like to support continued development, donations are greatly appreciated:

**Support Options:**
- **Venmo**: @BeauinTulsa
- **Ko-fi**: https://ko-fi.com/beaulewis

Your support helps fund:
- Continued development and feature enhancements
- Documentation and tutorial creation
- Community support and maintenance
- Research into new AI workflow methodologies

Every contribution, no matter the size, makes a difference and is deeply appreciated. Thank you for using MASTER-WORKFLOW and for considering supporting its development!

---

**Ready for production use** • **Actively maintained** • **Community driven**

*Transform your development workflow with intelligent automation and AI-powered orchestration.*