#!/bin/bash

# Security Validation Script
# Validates that all security measures are properly implemented

set -e

echo "🔐 MASTER-WORKFLOW Security Validation"
echo "======================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation results
VALIDATION_PASSED=true

check_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ PASS${NC}: $2"
    else
        echo -e "${RED}❌ FAIL${NC}: $2"
        VALIDATION_PASSED=false
    fi
}

warn_result() {
    echo -e "${YELLOW}⚠️  WARN${NC}: $1"
}

info_result() {
    echo -e "${BLUE}ℹ️  INFO${NC}: $1"
}

echo "1. Checking API Key Security..."
echo "------------------------------"

# Check if .mcp.json uses environment variables
if grep -q '\${.*}' .mcp.json 2>/dev/null; then
    check_result 0 ".mcp.json uses environment variables for secrets"
else
    check_result 1 ".mcp.json should use environment variables for all secrets"
fi

# Check for hardcoded API keys in .mcp.json
if grep -E '(sk-|ghp_|Bearer |JWT |token.*=|key.*=.*[a-zA-Z0-9]{20,})' .mcp.json 2>/dev/null | grep -v '\${' >/dev/null; then
    check_result 1 "Found potential hardcoded secrets in .mcp.json"
else
    check_result 0 "No hardcoded secrets found in .mcp.json"
fi

# Check if .env.example exists
if [ -f ".env.example" ]; then
    check_result 0 ".env.example template file exists"
else
    check_result 1 ".env.example template file missing"
fi

# Check if .env is protected by .gitignore
if grep -q "^\.env$" .gitignore 2>/dev/null; then
    check_result 0 ".env files are protected by .gitignore"
else
    check_result 1 ".env files should be protected by .gitignore"
fi

echo
echo "2. Checking File Permissions..."
echo "------------------------------"

# Check for overly permissive files
PERM_ISSUES=$(find . -type f -perm 777 2>/dev/null | head -5)
if [ -z "$PERM_ISSUES" ]; then
    check_result 0 "No files with 777 permissions found"
else
    check_result 1 "Found files with overly permissive 777 permissions"
    echo "   Examples: $(echo $PERM_ISSUES | tr '\n' ' ')"
fi

# Check script permissions
SCRIPT_PERMS=$(find . -name "*.sh" -type f ! -perm 755 2>/dev/null | head -3)
if [ -z "$SCRIPT_PERMS" ]; then
    check_result 0 "Shell scripts have appropriate permissions"
else
    warn_result "Some shell scripts may have non-standard permissions"
fi

echo
echo "3. Checking Documentation..."
echo "---------------------------"

# Check for security documentation
if [ -f "SECURITY.md" ]; then
    check_result 0 "SECURITY.md documentation exists"
else
    check_result 1 "SECURITY.md documentation missing"
fi

# Check if README mentions security setup
if grep -q -i "security\|\.env" README.md 2>/dev/null; then
    check_result 0 "README.md includes security setup instructions"
else
    check_result 1 "README.md should include security setup instructions"
fi

echo
echo "4. Checking Git Configuration..."
echo "-------------------------------"

# Check for .env files in git history
if git log --all --name-only | grep -q "\.env$" 2>/dev/null; then
    check_result 1 ".env files found in git history - consider cleaning"
else
    check_result 0 "No .env files found in git history"
fi

# Check if sensitive files are ignored
IGNORED_CHECK=$(git status --ignored 2>/dev/null | grep -E "\.env|api-key|secret" || true)
if [ ! -z "$IGNORED_CHECK" ]; then
    info_result "Sensitive files are properly ignored by git"
else
    warn_result "No sensitive files found to verify .gitignore rules"
fi

echo
echo "5. Environment Configuration Check..."
echo "-----------------------------------"

# Check if .env exists (should be local only)
if [ -f ".env" ]; then
    warn_result ".env file exists locally (good for development, ensure it's not committed)"
    
    # Check if .env has required variables
    REQUIRED_VARS="GITHUB_PERSONAL_ACCESS_TOKEN ANTHROPIC_API_KEY"
    for var in $REQUIRED_VARS; do
        if grep -q "^$var=" .env 2>/dev/null; then
            info_result "$var is configured in .env"
        else
            warn_result "$var is not configured in .env"
        fi
    done
else
    warn_result ".env file not found - copy from .env.example and configure"
fi

echo
echo "6. Additional Security Checks..."
echo "-------------------------------"

# Check for common security issues in package.json
if [ -f "package.json" ]; then
    if grep -q '"scripts"' package.json; then
        info_result "package.json found - consider running 'npm audit' for vulnerabilities"
    fi
fi

# Check for exposed secrets in common files
SECRET_FILES=$(find . -name "*.json" -o -name "*.js" -o -name "*.ts" -o -name "*.yml" -o -name "*.yaml" | head -20)
FOUND_SECRETS=""
for file in $SECRET_FILES; do
    if grep -l -E '(sk-[a-zA-Z0-9]{20,}|ghp_[a-zA-Z0-9]{36}|xox[a-zA-Z]-[a-zA-Z0-9-]+)' "$file" 2>/dev/null; then
        FOUND_SECRETS="$FOUND_SECRETS $file"
    fi
done

if [ -z "$FOUND_SECRETS" ]; then
    check_result 0 "No obvious API keys found in common files"
else
    check_result 1 "Potential API keys found in: $FOUND_SECRETS"
fi

echo
echo "🔐 SECURITY VALIDATION SUMMARY"
echo "============================="

if [ "$VALIDATION_PASSED" = true ]; then
    echo -e "${GREEN}✅ ALL SECURITY CHECKS PASSED${NC}"
    echo
    echo "Your MASTER-WORKFLOW installation is properly secured!"
    echo
    echo "Next steps:"
    echo "1. Configure your .env file with actual API keys"
    echo "2. Run 'npm audit' to check for dependency vulnerabilities"
    echo "3. Consider setting up automated security scanning"
    exit 0
else
    echo -e "${RED}❌ SECURITY VALIDATION FAILED${NC}"
    echo
    echo "Please address the failed checks above before proceeding."
    echo "See SECURITY.md for detailed remediation instructions."
    exit 1
fi